import sqlite3
import time
import threading
from typing import Optional, List, Tuple, Callable


class KeyTimeRecorder:
    """关键帧时间差录制器"""
    
    def __init__(self, db_path: str):
        """初始化时间录制器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.is_recording = False
        self.current_image_id = None
        self.recording_start_time = None
        self.last_keyframe_time = None
        self.current_keyframe_index = 0
        self.timing_sequence = []
        
        # 原图模式相关属性
        self.is_original_mode_recording = False  # 是否为原图模式录制
        self.original_mode_sequence = []  # 原图模式的时间序列
        self.current_similar_index = 0  # 当前相似图片索引
        self.similar_images = []  # 相似图片列表
        self.last_image_switch_time = None  # 上次图片切换时间
        
        # 初始化数据库表
        self._init_timing_table()
        self._init_original_mode_table()
    
    def _init_timing_table(self):
        """初始化时间记录表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS keyframe_timings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        image_id INTEGER NOT NULL,
                        keyframe_id INTEGER NOT NULL,
                        duration REAL NOT NULL,
                        sequence_order INTEGER NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (image_id) REFERENCES images(id),
                        FOREIGN KEY (keyframe_id) REFERENCES keyframes(id)
                    )
                ''')
                
                # 创建索引
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timing_image ON keyframe_timings(image_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timing_sequence ON keyframe_timings(image_id, sequence_order)')
                
                conn.commit()
        except Exception as e:
            print(f"初始化时间记录表失败: {e}")
    
    def _init_original_mode_table(self):
        """初始化原图模式时间记录表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS original_mode_timings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        base_image_id INTEGER NOT NULL,
                        from_image_id INTEGER NOT NULL,
                        to_image_id INTEGER NOT NULL,
                        duration REAL NOT NULL,
                        sequence_order INTEGER NOT NULL,
                        mark_type TEXT DEFAULT 'loop',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (base_image_id) REFERENCES images(id),
                        FOREIGN KEY (from_image_id) REFERENCES images(id),
                        FOREIGN KEY (to_image_id) REFERENCES images(id)
                    )
                ''')
                
                # 创建索引
                conn.execute('CREATE INDEX IF NOT EXISTS idx_original_base ON original_mode_timings(base_image_id)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_original_sequence ON original_mode_timings(base_image_id, sequence_order)')
                
                conn.commit()
        except Exception as e:
            print(f"初始化原图模式时间记录表失败: {e}")
    
    def start_original_mode_recording(self, base_image_id: int, similar_images: List[Tuple[int, str, str]], mark_type: str = 'loop') -> bool:
        """开始原图模式录制（记录相似图片之间的切换时间）
        
        Args:
            base_image_id: 基础图片ID（用于标识相似图片组）
            similar_images: 相似图片列表 [(image_id, name, path), ...]
            mark_type: 标记类型 ('loop' 或 'sequence')
            
        Returns:
            bool: 是否成功开始录制
        """
        if self.is_original_mode_recording:
            print("原图模式已经在录制中，请先停止当前录制")
            return False
        
        if not similar_images:
            print("没有相似图片，无法开始原图模式录制")
            return False
        
        self.is_original_mode_recording = True
        self.current_image_id = base_image_id
        self.similar_images = similar_images
        self.current_similar_index = 0
        self.original_mode_sequence = []
        self.recording_start_time = time.time()
        self.last_image_switch_time = self.recording_start_time
        
        # 清除该基础图片的旧原图模式时间记录
        self.clear_original_mode_timing_data(base_image_id)
        
        print(f"开始原图模式录制，基础图片ID: {base_image_id}，相似图片数量: {len(similar_images)}，标记类型: {mark_type}")
        return True
    
    def stop_original_mode_recording(self) -> bool:
        """停止原图模式录制
        
        Returns:
            bool: 是否成功停止录制
        """
        if not self.is_original_mode_recording:
            print("当前没有在原图模式录制")
            return False
        
        self.is_original_mode_recording = False
        
        # 保存录制的时间序列到数据库
        if self.original_mode_sequence:
            self._save_original_mode_sequence()
        
        print(f"停止原图模式录制，共录制了 {len(self.original_mode_sequence)} 个图片切换时间间隔")
        
        # 重置状态
        self.current_image_id = None
        self.similar_images = []
        self.current_similar_index = 0
        self.original_mode_sequence = []
        self.recording_start_time = None
        self.last_image_switch_time = None
        
        return True
    
    def record_image_switch_timing(self, from_image_id: int, to_image_id: int, manual_duration: Optional[float] = None) -> bool:
        """记录图片切换时间（原图模式）
        
        Args:
            from_image_id: 源图片ID
            to_image_id: 目标图片ID
            manual_duration: 手动指定的时间（秒），如果不提供则自动计算
            
        Returns:
            bool: 是否成功记录
        """
        if not self.is_original_mode_recording:
            return False
        
        current_time = time.time()
        
        if manual_duration is not None:
            duration = manual_duration
        else:
            # 计算从上一个图片切换到现在的时间间隔
            duration = current_time - self.last_image_switch_time
        
        # 记录到时间序列
        timing_info = {
            'from_image_id': from_image_id,
            'to_image_id': to_image_id,
            'duration': duration,
            'sequence_order': len(self.original_mode_sequence),
            'timestamp': current_time
        }
        
        self.original_mode_sequence.append(timing_info)
        
        # 更新状态
        self.last_image_switch_time = current_time
        
        print(f"记录图片切换时间: {from_image_id} -> {to_image_id}, 停留时间: {duration:.2f}秒")
        return True
    
    def _save_original_mode_sequence(self):
        """保存原图模式时间序列到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for timing in self.original_mode_sequence:
                    conn.execute('''
                        INSERT INTO original_mode_timings 
                        (base_image_id, from_image_id, to_image_id, duration, sequence_order, mark_type)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        self.current_image_id,
                        timing['from_image_id'],
                        timing['to_image_id'],
                        timing['duration'],
                        timing['sequence_order'],
                        'loop'  # 默认循环模式
                    ))
                
                conn.commit()
                print(f"成功保存 {len(self.original_mode_sequence)} 个原图模式时间记录到数据库")
        except Exception as e:
            print(f"保存原图模式时间序列失败: {e}")
    
    def get_original_mode_timing_sequence(self, base_image_id: int) -> List[Tuple[int, int, float, int]]:
        """获取原图模式的完整时间序列
        
        Args:
            base_image_id: 基础图片ID
            
        Returns:
            List[Tuple[int, int, float, int]]: (from_image_id, to_image_id, duration, sequence_order) 的列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT from_image_id, to_image_id, duration, sequence_order
                    FROM original_mode_timings
                    WHERE base_image_id = ?
                    ORDER BY sequence_order
                ''', (base_image_id,))
                
                return cursor.fetchall()
        except Exception as e:
            print(f"获取原图模式时间序列失败: {e}")
            return []
    
    def clear_original_mode_timing_data(self, base_image_id: int) -> bool:
        """清除指定基础图片的原图模式时间数据
        
        Args:
            base_image_id: 基础图片ID
            
        Returns:
            bool: 是否成功清除
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM original_mode_timings WHERE base_image_id = ?', (base_image_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"清除原图模式时间数据失败: {e}")
            return False
    
    def has_original_mode_timing_data(self, base_image_id: int) -> bool:
        """检查是否有原图模式时间数据
        
        Args:
            base_image_id: 基础图片ID
            
        Returns:
            bool: 是否有原图模式时间数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT COUNT(*) FROM original_mode_timings WHERE base_image_id = ?',
                    (base_image_id,)
                )
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            print(f"检查原图模式时间数据失败: {e}")
            return False
    
    def start_recording(self, image_id: int) -> bool:
        """开始录制时间差
        
        Args:
            image_id: 图片ID
            
        Returns:
            bool: 是否成功开始录制
        """
        if self.is_recording:
            print("已经在录制中，请先停止当前录制")
            return False
        
        self.current_image_id = image_id
        self.is_recording = True
        self.recording_start_time = time.time()
        self.last_keyframe_time = self.recording_start_time
        self.current_keyframe_index = 0
        self.timing_sequence = []
        
        # 清除该图片的旧时间记录
        self.clear_timing_data(image_id)
        
        #print(f"开始录制图片 {image_id} 的关键帧时间差")
        return True
    
    def stop_recording(self) -> bool:
        """停止录制
        
        Returns:
            bool: 是否成功停止录制
        """
        if not self.is_recording:
            print("当前没有在录制")
            return False
        
        self.is_recording = False
        
        # 保存录制的时间序列到数据库
        if self.timing_sequence:
            self._save_timing_sequence()
        
        #print(f"停止录制，共录制了 {len(self.timing_sequence)} 个时间间隔")
        
        # 重置状态
        self.current_image_id = None
        self.recording_start_time = None
        self.last_keyframe_time = None
        self.current_keyframe_index = 0
        self.timing_sequence = []
        
        return True
    
    def record_keyframe_timing(self, keyframe_id: int, manual_duration: Optional[float] = None) -> bool:
        """记录关键帧停留时间

        Args:
            keyframe_id: 关键帧ID
            manual_duration: 手动指定的时间（秒），如果不提供则自动计算

        Returns:
            bool: 是否成功记录
        """
        if not self.is_recording:
            return False

        current_time = time.time()

        if manual_duration is not None:
            duration = manual_duration
        else:
            # 计算从上一个关键帧到现在的时间间隔
            duration = current_time - self.last_keyframe_time

        # 记录到时间序列
        timing_info = {
            'keyframe_id': keyframe_id,
            'duration': duration,
            'sequence_order': self.current_keyframe_index,
            'timestamp': current_time
        }

        self.timing_sequence.append(timing_info)

        # 更新状态
        self.last_keyframe_time = current_time
        self.current_keyframe_index += 1

        #print(f"记录关键帧 {keyframe_id} 停留时间: {duration:.2f}秒")
        return True

    def update_keyframe_timing_in_db(self, image_id: int, keyframe_id: int, new_duration: float) -> bool:
        """实时更新数据库中的关键帧时间

        Args:
            image_id: 图片ID
            keyframe_id: 关键帧ID
            new_duration: 新的停留时间（秒）

        Returns:
            bool: 是否成功更新
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 更新数据库中的时间记录
                cursor = conn.execute('''
                    UPDATE keyframe_timings
                    SET duration = ?
                    WHERE image_id = ? AND keyframe_id = ?
                ''', (new_duration, image_id, keyframe_id))

                if cursor.rowcount > 0:
                    conn.commit()
                    print(f"实时修正关键帧 {keyframe_id} 的停留时间为: {new_duration:.2f}秒")
                    return True
                else:
                    print(f"未找到关键帧 {keyframe_id} 的时间记录")
                    return False

        except Exception as e:
            print(f"更新关键帧时间失败: {e}")
            return False

    def update_original_mode_timing_in_db(self, base_image_id: int, from_image_id: int, to_image_id: int, new_duration: float) -> bool:
        """实时更新数据库中的原图模式时间

        Args:
            base_image_id: 基础图片ID
            from_image_id: 源图片ID
            to_image_id: 目标图片ID
            new_duration: 新的停留时间（秒）

        Returns:
            bool: 是否成功更新
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 更新数据库中的时间记录
                cursor = conn.execute('''
                    UPDATE original_mode_timings
                    SET duration = ?
                    WHERE base_image_id = ? AND from_image_id = ? AND to_image_id = ?
                ''', (new_duration, base_image_id, from_image_id, to_image_id))

                if cursor.rowcount > 0:
                    conn.commit()
                    print(f"实时修正原图模式时间：{from_image_id} -> {to_image_id} 时间修正为 {new_duration:.2f}秒")
                    return True
                else:
                    print(f"未找到原图模式时间记录：{from_image_id} -> {to_image_id}")
                    return False

        except Exception as e:
            print(f"更新原图模式时间失败: {e}")
            return False
    
    def _save_timing_sequence(self):
        """保存时间序列到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for timing in self.timing_sequence:
                    conn.execute('''
                        INSERT INTO keyframe_timings 
                        (image_id, keyframe_id, duration, sequence_order)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        self.current_image_id,
                        timing['keyframe_id'],
                        timing['duration'],
                        timing['sequence_order']
                    ))
                
                conn.commit()
                #print(f"成功保存 {len(self.timing_sequence)} 个时间记录到数据库")
        except Exception as e:
            print(f"保存时间序列失败: {e}")
    
    def get_timing_sequence(self, image_id: int) -> List[Tuple[int, float, int]]:
        """获取图片的完整时间序列
        
        Args:
            image_id: 图片ID
            
        Returns:
            List[Tuple[int, float, int]]: (keyframe_id, duration, sequence_order) 的列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT keyframe_id, duration, sequence_order
                    FROM keyframe_timings
                    WHERE image_id = ?
                    ORDER BY sequence_order
                ''', (image_id,))
                
                return cursor.fetchall()
        except Exception as e:
            print(f"获取时间序列失败: {e}")
            return []
    
    def clear_timing_data(self, image_id: int) -> bool:
        """清除指定图片的时间数据
        
        Args:
            image_id: 图片ID
            
        Returns:
            bool: 是否成功清除
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM keyframe_timings WHERE image_id = ?', (image_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"清除时间数据失败: {e}")
            return False
    
    def has_timing_data(self, image_id: int) -> bool:
        """检查是否有时间数据
        
        Args:
            image_id: 图片ID
            
        Returns:
            bool: 是否有时间数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT COUNT(*) FROM keyframe_timings WHERE image_id = ?',
                    (image_id,)
                )
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            print(f"检查时间数据失败: {e}")
            return False


class AutoPlayer:
    """自动播放控制器"""
    
    def __init__(self, main_app, time_recorder: KeyTimeRecorder):
        """初始化自动播放器
        
        Args:
            main_app: 主程序实例（ImageProjector）
            time_recorder: 时间录制器实例
        """
        self.main_app = main_app
        self.time_recorder = time_recorder
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.loop_enabled = True
        self.play_speed = 1.0  # 播放速度倍率
        
        # 播放控制
        self.current_image_id = None
        self.timing_sequence = []
        self.current_sequence_index = 0
        self.play_timer_id = None
        
        # 播放次数控制
        self.target_play_count = 1  # 目标播放次数，-1表示无限循环
        self.completed_play_count = 0  # 已完成的播放次数
        
        # 播放统计
        self.play_start_time = None
        self.total_play_time = 0
        self.loop_count = 0
        
        # 播放结束回调函数
        self.on_play_finished = None

        # 原图模式播放相关属性
        self.is_original_mode_playing = False  # 是否为原图模式播放
        self.original_mode_sequence = []  # 原图模式的时间序列
        self.current_original_sequence_index = 0  # 原图模式序列索引
        self.similar_images = []  # 相似图片列表
        self.current_similar_index = 0  # 当前相似图片索引
        self.base_image_id = None  # 基础图片ID

        # 播放时间修正相关属性
        self.manual_correction_enabled = True  # 是否启用手动修正
        self.last_manual_operation_time = None  # 上次手动操作时间
        self.current_frame_start_time = None  # 当前帧开始时间
    
    def start_auto_play(self, image_id: int) -> bool:
        """开始自动播放
        
        Args:
            image_id: 图片ID
            
        Returns:
            bool: 是否成功开始播放
        """
        if self.is_playing:
            print("已经在播放中")
            return False
        
        # 获取时间序列
        self.timing_sequence = self.time_recorder.get_timing_sequence(image_id)
        if not self.timing_sequence:
            print(f"图片 {image_id} 没有时间序列数据")
            return False
        
        self.current_image_id = image_id
        self.is_playing = True
        self.is_paused = False
        self.current_sequence_index = 0
        self.play_start_time = time.time()
        self.loop_count = 0
        self.completed_play_count = 0  # 重置已完成播放次数

        # 初始化时间修正相关变量
        self.current_frame_start_time = time.time()
        self.last_manual_operation_time = None

        #print(f"开始自动播放图片 {image_id}，共 {len(self.timing_sequence)} 个关键帧")

        # 立即播放第一帧
        self._play_next_frame()
        return True
    
    def stop_auto_play(self) -> bool:
        """停止自动播放
        
        Returns:
            bool: 是否成功停止播放
        """
        if not self.is_playing:
            print("当前没有在播放")
            return False
        
        self.is_playing = False
        self.is_paused = False
        
        # 取消定时器
        if self.play_timer_id:
            self.main_app.root.after_cancel(self.play_timer_id)
            self.play_timer_id = None
        
        # 计算总播放时间
        if self.play_start_time:
            self.total_play_time += time.time() - self.play_start_time
        
        #print(f"停止自动播放，总播放时间: {self.total_play_time:.2f}秒，循环次数: {self.loop_count}")
        
        # 调用播放结束回调函数
        if self.on_play_finished:
            self.on_play_finished()
        
        # 重置状态
        self.current_image_id = None
        self.timing_sequence = []
        self.current_sequence_index = 0
        self.play_start_time = None
        
        return True

    def record_manual_operation(self, keyframe_id: int) -> bool:
        """记录手动操作，用于实时修正播放时间

        Args:
            keyframe_id: 当前关键帧ID

        Returns:
            bool: 是否成功记录
        """
        if not self.is_playing or not self.manual_correction_enabled:
            return False

        current_time = time.time()

        # 如果有上次手动操作时间，计算实际停留时间
        if self.current_frame_start_time is not None:
            actual_duration = current_time - self.current_frame_start_time

            # 更新数据库中的时间记录
            if self.time_recorder.update_keyframe_timing_in_db(
                self.current_image_id, keyframe_id, actual_duration):

                # 同时更新内存中的时间序列
                self._update_timing_sequence_in_memory(keyframe_id, actual_duration)

                print(f"播放时实时修正：关键帧 {keyframe_id} 时间修正为 {actual_duration:.2f}秒")

        # 记录当前操作时间，作为下一帧的开始时间
        self.current_frame_start_time = current_time
        self.last_manual_operation_time = current_time

        return True

    def _update_timing_sequence_in_memory(self, keyframe_id: int, new_duration: float):
        """更新内存中的时间序列

        Args:
            keyframe_id: 关键帧ID
            new_duration: 新的停留时间
        """
        for i, (kf_id, duration, sequence_order) in enumerate(self.timing_sequence):
            if kf_id == keyframe_id:
                # 更新时间序列中的时间
                self.timing_sequence[i] = (kf_id, new_duration, sequence_order)
                break

    def record_original_mode_manual_operation(self, from_image_id: int, to_image_id: int) -> bool:
        """记录原图模式手动操作，用于实时修正播放时间

        Args:
            from_image_id: 源图片ID
            to_image_id: 目标图片ID

        Returns:
            bool: 是否成功记录
        """
        if not self.is_original_mode_playing or not self.manual_correction_enabled:
            return False

        current_time = time.time()

        # 如果有上次手动操作时间，计算实际停留时间
        if self.current_frame_start_time is not None:
            actual_duration = current_time - self.current_frame_start_time

            # 更新数据库中的时间记录
            if self.time_recorder.update_original_mode_timing_in_db(
                self.base_image_id, from_image_id, to_image_id, actual_duration):

                # 同时更新内存中的时间序列
                self._update_original_mode_timing_sequence_in_memory(from_image_id, to_image_id, actual_duration)

                print(f"原图模式播放时实时修正：{from_image_id} -> {to_image_id} 时间修正为 {actual_duration:.2f}秒")

        # 记录当前操作时间，作为下一帧的开始时间
        self.current_frame_start_time = current_time
        self.last_manual_operation_time = current_time

        return True

    def _update_original_mode_timing_sequence_in_memory(self, from_image_id: int, to_image_id: int, new_duration: float):
        """更新内存中的原图模式时间序列

        Args:
            from_image_id: 源图片ID
            to_image_id: 目标图片ID
            new_duration: 新的停留时间
        """
        for i, (from_id, to_id, duration, sequence_order) in enumerate(self.original_mode_sequence):
            if from_id == from_image_id and to_id == to_image_id:
                # 更新时间序列中的时间
                self.original_mode_sequence[i] = (from_id, to_id, new_duration, sequence_order)
                break

    def pause_auto_play(self) -> bool:
        """暂停自动播放
        
        Returns:
            bool: 是否成功暂停
        """
        if not self.is_playing or self.is_paused:
            return False
        
        self.is_paused = True
        
        # 取消当前定时器
        if self.play_timer_id:
            self.main_app.root.after_cancel(self.play_timer_id)
            self.play_timer_id = None
        
        # 更新播放时间
        if self.play_start_time:
            self.total_play_time += time.time() - self.play_start_time
            self.play_start_time = None
        
        print("暂停自动播放")
        return True
    
    def resume_auto_play(self) -> bool:
        """恢复自动播放
        
        Returns:
            bool: 是否成功恢复
        """
        if not self.is_playing or not self.is_paused:
            return False
        
        self.is_paused = False
        self.play_start_time = time.time()
        
        # 继续播放当前帧的剩余时间
        self._schedule_next_frame_with_remaining_time()
        
        print("恢复自动播放")
        return True
    
    def set_loop_mode(self, enabled: bool):
        """设置循环模式
        
        Args:
            enabled: 是否启用循环
        """
        self.loop_enabled = enabled
        print(f"循环模式: {'启用' if enabled else '禁用'}")
    
    def set_play_speed(self, speed: float):
        """设置播放速度
        
        Args:
            speed: 播放速度倍率 (0.1 - 5.0)
        """
        if speed <= 0:
            speed = 1.0
        
        self.play_speed = max(0.1, min(5.0, speed))
        print(f"播放速度设置为: {self.play_speed}x")
    
    def _play_next_frame(self):
        """播放下一帧"""
        if not self.is_playing or self.is_paused:
            return
        
        if not self.timing_sequence:
            self.stop_auto_play()
            return
        
        # 检查是否需要循环或首次播放
        is_direct_jump = False
        
        # 首次播放检查
        if self.current_sequence_index == 0 and self.loop_count == 0:
            is_direct_jump = True  # 首次播放直接跳转到第一帧
        
        # 循环检查
        if self.current_sequence_index >= len(self.timing_sequence):
            # 一轮播放完成
            self.completed_play_count += 1
            
            # 检查是否需要继续播放
            should_continue = False
            if self.target_play_count == -1:  # 无限循环
                should_continue = True
            elif self.completed_play_count < self.target_play_count:  # 还没达到目标次数
                should_continue = True
            
            if should_continue:
                self.current_sequence_index = 0
                self.loop_count += 1
                is_direct_jump = True  # 循环回第一帧时直接跳转
                remaining = "∞" if self.target_play_count == -1 else (self.target_play_count - self.completed_play_count)
                #print(f"完成第 {self.completed_play_count} 次播放，开始第 {self.completed_play_count + 1} 次播放（剩余：{remaining}次）")
            else:
                #print(f"播放完成，共播放 {self.completed_play_count} 次")
                self.stop_auto_play()
                return
        
        # 获取当前关键帧信息
        keyframe_id, duration, sequence_order = self.timing_sequence[self.current_sequence_index]

        # 调用主程序跳转到下一关键帧，首次播放或循环回第一帧时使用直接跳转
        self._jump_to_keyframe(self.current_sequence_index, use_direct_jump=is_direct_jump)

        # 记录当前帧开始时间（用于手动修正）
        self.current_frame_start_time = time.time()

        # 计算调整后的等待时间
        adjusted_duration = duration / self.play_speed

        #print(f"播放第 {self.current_sequence_index + 1}/{len(self.timing_sequence)} 帧，等待 {adjusted_duration:.2f}秒")

        # 准备播放下一帧
        self.current_sequence_index += 1

        # 安排下一帧播放
        self._schedule_next_frame(adjusted_duration)
    
    def _jump_to_keyframe(self, sequence_index: int, use_direct_jump: bool = False):
        """跳转到指定序列的关键帧
        
        Args:
            sequence_index: 序列索引
            use_direct_jump: 是否使用直接跳转（不使用滚动动画）
        """
        try:
            if sequence_index >= len(self.timing_sequence):
                return
            
            # 获取关键帧信息
            keyframe_id, duration, sequence_order = self.timing_sequence[sequence_index]
            
            # 获取主程序中对应的关键帧位置
            keyframes = self.main_app.get_keyframes(self.current_image_id)
            target_position = None
            
            for i, (kf_id, position, order) in enumerate(keyframes):
                if kf_id == keyframe_id:
                    target_position = position
                    # 更新主程序的关键帧索引
                    self.main_app.current_keyframe_index = i
                    break
            
            if target_position is not None:
                # 智能判断：如果录制的停留时间小于滚动动画时间，则使用直接跳转
                scroll_duration = getattr(self.main_app, 'scroll_duration', 1.0)
                adjusted_duration = duration / self.play_speed
                
                # 如果停留时间小于滚动动画时间，强制使用直接跳转
                if adjusted_duration < scroll_duration:
                    use_direct_jump = True
                    #print(f"智能跳转：停留时间({adjusted_duration:.2f}s) < 滚动时间({scroll_duration:.2f}s)，使用直接跳转")
                
                if use_direct_jump:
                    # 直接跳转，不使用滚动动画（用于循环回第一帧或短时间停留）
                    self.main_app.canvas.yview_moveto(target_position)
                    
                    # 同步投影屏幕
                    if (hasattr(self.main_app, 'second_window') and 
                        self.main_app.second_window and 
                        hasattr(self.main_app, 'sync_enabled') and 
                        self.main_app.sync_enabled):
                        self.main_app.sync_projection_screen_absolute()
                else:
                    # 使用平滑滚动到目标位置
                    self.main_app.smooth_scroll_to(target_position)
                
                # 更新指示器
                if hasattr(self.main_app, 'update_keyframe_indicators'):
                    self.main_app.update_keyframe_indicators()
                
                # 更新预览线
                if hasattr(self.main_app, 'update_preview_lines'):
                    self.main_app.update_preview_lines()
                
                # 确保投影窗口也更新
                if (hasattr(self.main_app, 'second_window') and 
                    self.main_app.second_window and 
                    hasattr(self.main_app, 'sync_enabled') and 
                    self.main_app.sync_enabled):
                    self.main_app.update_projection()
            
        except Exception as e:
            print(f"跳转到关键帧失败: {e}")
    
    def _schedule_next_frame(self, delay_seconds: float):
        """安排下一帧播放
        
        Args:
            delay_seconds: 延时秒数
        """
        if not self.is_playing or self.is_paused:
            return
        
        # 转换为毫秒
        delay_ms = int(delay_seconds * 1000)
        
        # 使用tkinter的after方法安排下一帧
        self.play_timer_id = self.main_app.root.after(delay_ms, self._play_next_frame)
    
    def _schedule_next_frame_with_remaining_time(self):
        """恢复播放时，使用剩余时间安排下一帧播放"""
        if not self.timing_sequence or self.current_sequence_index >= len(self.timing_sequence):
            self._play_next_frame()
            return
        
        # 使用原始时间间隔的一半作为剩余时间（简化处理）
        _, duration, _ = self.timing_sequence[self.current_sequence_index - 1] if self.current_sequence_index > 0 else (0, 1.0, 0)
        remaining_time = (duration / self.play_speed) * 0.5
        
        self._schedule_next_frame(remaining_time)
    
    def get_play_status(self) -> dict:
        """获取播放状态信息
        
        Returns:
            dict: 播放状态信息
        """
        return {
            'is_playing': self.is_playing,
            'is_paused': self.is_paused,
            'loop_enabled': self.loop_enabled,
            'play_speed': self.play_speed,
            'current_frame': self.current_sequence_index,
            'total_frames': len(self.timing_sequence),
            'loop_count': self.loop_count,
            'total_play_time': self.total_play_time
        }
    
    def skip_to_frame(self, frame_index: int) -> bool:
        """跳转到指定帧
        
        Args:
            frame_index: 帧索引
            
        Returns:
            bool: 是否成功跳转
        """
        if not self.timing_sequence or frame_index < 0 or frame_index >= len(self.timing_sequence):
            return False
        
        self.current_sequence_index = frame_index
        
        # 如果正在播放，重新安排播放
        if self.is_playing and not self.is_paused:
            if self.play_timer_id:
                self.main_app.root.after_cancel(self.play_timer_id)
            
            self._play_next_frame()
        
        return True

    def start_original_mode_auto_play(self, base_image_id: int, similar_images: List[Tuple[int, str, str]]) -> bool:
        """开始原图模式自动播放
        
        Args:
            base_image_id: 基础图片ID
            similar_images: 相似图片列表 [(image_id, name, path), ...]
            
        Returns:
            bool: 是否成功开始播放
        """
        if self.is_original_mode_playing:
            print("原图模式已经在播放中")
            return False
        
        # 获取原图模式时间序列
        self.original_mode_sequence = self.time_recorder.get_original_mode_timing_sequence(base_image_id)
        if not self.original_mode_sequence:
            print(f"基础图片 {base_image_id} 没有原图模式时间序列数据")
            return False
        
        self.base_image_id = base_image_id
        self.similar_images = similar_images
        self.is_original_mode_playing = True
        self.is_paused = False
        self.current_original_sequence_index = 0
        self.current_similar_index = 0
        self.play_start_time = time.time()
        self.loop_count = 0
        self.completed_play_count = 0

        # 初始化原图模式时间修正相关变量
        self.current_frame_start_time = time.time()
        self.last_manual_operation_time = None

        # 立即播放第一帧
        self._play_next_original_frame()
        return True
    
    def stop_original_mode_auto_play(self) -> bool:
        """停止原图模式自动播放
        
        Returns:
            bool: 是否成功停止播放
        """
        if not self.is_original_mode_playing:
            print("当前没有在原图模式播放")
            return False
        
        self.is_original_mode_playing = False
        self.is_paused = False
        
        # 取消定时器
        if self.play_timer_id:
            self.main_app.root.after_cancel(self.play_timer_id)
            self.play_timer_id = None
        
        # 计算总播放时间
        if self.play_start_time:
            self.total_play_time += time.time() - self.play_start_time
        
        print(f"停止原图模式自动播放，总播放时间: {self.total_play_time:.2f}秒，循环次数: {self.loop_count}")
        
        # 调用播放结束回调函数
        if self.on_play_finished:
            self.on_play_finished()
        
        # 重置状态
        self.base_image_id = None
        self.similar_images = []
        self.original_mode_sequence = []
        self.current_original_sequence_index = 0
        self.current_similar_index = 0
        self.play_start_time = None
        
        return True
    
    def pause_original_mode_auto_play(self) -> bool:
        """暂停原图模式自动播放
        
        Returns:
            bool: 是否成功暂停
        """
        if not self.is_original_mode_playing or self.is_paused:
            return False
        
        self.is_paused = True
        
        # 取消当前定时器
        if self.play_timer_id:
            self.main_app.root.after_cancel(self.play_timer_id)
            self.play_timer_id = None
        
        # 更新播放时间
        if self.play_start_time:
            self.total_play_time += time.time() - self.play_start_time
            self.play_start_time = None
        
        print("暂停原图模式自动播放")
        return True
    
    def resume_original_mode_auto_play(self) -> bool:
        """恢复原图模式自动播放
        
        Returns:
            bool: 是否成功恢复
        """
        if not self.is_original_mode_playing or not self.is_paused:
            return False
        
        self.is_paused = False
        self.play_start_time = time.time()
        
        # 继续播放当前帧的剩余时间
        self._schedule_next_original_frame_with_remaining_time()
        
        print("恢复原图模式自动播放")
        return True
    
    def _play_next_original_frame(self):
        """播放原图模式下一帧"""
        if not self.is_original_mode_playing or self.is_paused:
            return
        
        if not self.original_mode_sequence:
            self.stop_original_mode_auto_play()
            return
        
        # 检查是否需要循环或首次播放
        is_direct_jump = False
        
        # 如果是循环重新开始，设置直接跳转标志
        if self.loop_count > 0 and self.current_original_sequence_index == 0:
            is_direct_jump = True
        
        # 循环检查 - 这个逻辑现在由 _perform_delayed_switch 处理
        # 这里只处理循环开始时的状态重置
        if self.current_original_sequence_index >= len(self.original_mode_sequence):
            # 这种情况不应该发生，因为循环逻辑已经移到 _perform_delayed_switch
            return
        
        # 获取当前切换信息
        from_image_id, to_image_id, duration, sequence_order = self.original_mode_sequence[self.current_original_sequence_index]

        # 记录当前帧开始时间（用于手动修正）
        self.current_frame_start_time = time.time()

        # 计算调整后的等待时间
        adjusted_duration = duration / self.play_speed
        
        # 首次播放或每次循环的第一帧，先显示from_image_id，等待后切换到to_image_id
        if (self.current_original_sequence_index == 0 and self.loop_count == 0) or is_direct_jump:
            # 如果是循环重新开始，使用最后一帧的时间（图4-图1的时间）
            if is_direct_jump and self.loop_count > 0:
                # 循环重新开始，直接切到图1，等待图1→图2的时间
                self._switch_to_similar_image(from_image_id, use_direct_jump=True)
                self._schedule_delayed_switch(to_image_id, adjusted_duration)
            else:
                # 首次播放，使用第一帧的时间
                self._switch_to_similar_image(from_image_id, use_direct_jump=True)
                self._schedule_delayed_switch(to_image_id, adjusted_duration)
            # 注意：index 递增放在 _perform_delayed_switch
            return
        else:
            # 正常播放：直接切换到目标图片
            self._switch_to_similar_image(to_image_id, use_direct_jump=is_direct_jump)
            self.current_original_sequence_index += 1
            
            # 检查是否是最后一帧
            if self.current_original_sequence_index >= len(self.original_mode_sequence):
                # 最后一帧：直接进入循环判断，不再等待额外时间
                # 模拟最后一帧的处理，进入循环判断
                self._perform_delayed_switch(to_image_id)
                return
            else:
                # 不是最后一帧：安排下一帧播放
                next_from_id, next_to_id, next_duration, next_order = self.original_mode_sequence[self.current_original_sequence_index]
                next_adjusted_duration = next_duration / self.play_speed
                self._schedule_next_original_frame(next_adjusted_duration)

    def _switch_to_similar_image(self, target_image_id: int, use_direct_jump: bool = False):
        """切换到指定的相似图片
        
        Args:
            target_image_id: 目标图片ID
            use_direct_jump: 是否使用直接跳转
        """
        try:
            # 在相似图片列表中找到目标图片
            target_path = None
            target_name = None
            
            for img_id, img_name, img_path in self.similar_images:
                if img_id == target_image_id:
                    target_path = img_path
                    target_name = img_name
                    break
            
            if target_path:
                # 更新当前相似图片索引
                for i, (img_id, _, _) in enumerate(self.similar_images):
                    if img_id == target_image_id:
                        self.current_similar_index = i
                        break
                
                # 加载目标图片
                self.main_app.load_image(target_path)
                
                # 在项目树中选中该图片
                self.main_app.project_tree.selection_set(str(target_image_id))
                self.main_app.project_tree.see(str(target_image_id))
                
                # 强制更新投影窗口
                if hasattr(self.main_app, 'force_update_projection'):
                    self.main_app.force_update_projection()
                
                print(f"切换到相似图片: {target_name} (ID: {target_image_id})")
            else:
                print(f"未找到目标图片 ID: {target_image_id}")
                
        except Exception as e:
            print(f"切换到相似图片失败: {e}")
    
    def _schedule_next_original_frame(self, delay_seconds: float):
        """安排原图模式下一帧播放
        
        Args:
            delay_seconds: 延时秒数
        """
        if not self.is_original_mode_playing or self.is_paused:
            return
        
        # 转换为毫秒
        delay_ms = int(delay_seconds * 1000)
        
        # 使用tkinter的after方法安排下一帧
        self.play_timer_id = self.main_app.root.after(delay_ms, self._play_next_original_frame)
    
    def _schedule_delayed_switch(self, target_image_id: int, delay_seconds: float):
        """安排延迟切换到指定图片"""
        if not self.is_original_mode_playing or self.is_paused:
            return
        delay_ms = int(delay_seconds * 1000)
        self.play_timer_id = self.main_app.root.after(delay_ms, lambda: self._perform_delayed_switch(target_image_id))

    def _perform_delayed_switch(self, target_image_id: int):
        """执行延迟切换"""
        if not self.is_original_mode_playing or self.is_paused:
            return
        self._switch_to_similar_image(target_image_id, use_direct_jump=False)
        self.current_original_sequence_index += 1
        
        # 如果已经是最后一帧，进行循环判断
        if self.current_original_sequence_index >= len(self.original_mode_sequence):
            # 一轮播放完成
            self.completed_play_count += 1
            
            # 检查是否需要继续播放
            should_continue = False
            if self.target_play_count == -1:  # 无限循环
                should_continue = True
            elif self.completed_play_count < self.target_play_count:  # 还没达到目标次数
                should_continue = True
            
            if should_continue:
                self.current_original_sequence_index = 0
                self.current_similar_index = 0
                self.loop_count += 1
                # 立即开始下一轮，无缝衔接
                self._play_next_original_frame()
            else:
                self.stop_original_mode_auto_play()
                if self.on_play_finished:
                    self.on_play_finished()
            return
        
        # 否则，安排下一帧
        next_from_id, next_to_id, next_duration, next_order = self.original_mode_sequence[self.current_original_sequence_index]
        next_adjusted_duration = next_duration / self.play_speed
        self._schedule_next_original_frame(next_adjusted_duration)



    def _schedule_next_original_frame_with_remaining_time(self):
        """恢复原图模式播放时，使用剩余时间安排下一帧播放"""
        if not self.original_mode_sequence or self.current_original_sequence_index >= len(self.original_mode_sequence):
            self._play_next_original_frame()
            return
        
        # 使用原始时间间隔的一半作为剩余时间（简化处理）
        _, _, duration, _ = self.original_mode_sequence[self.current_original_sequence_index - 1] if self.current_original_sequence_index > 0 else (0, 0, 1.0, 0)
        remaining_time = (duration / self.play_speed) * 0.5
        
        self._schedule_next_original_frame(remaining_time)
    
    def get_original_mode_play_status(self) -> dict:
        """获取原图模式播放状态信息
        
        Returns:
            dict: 播放状态信息
        """
        return {
            'is_playing': self.is_original_mode_playing,
            'is_paused': self.is_paused,
            'loop_enabled': self.loop_enabled,
            'play_speed': self.play_speed,
            'current_frame': self.current_original_sequence_index,
            'total_frames': len(self.original_mode_sequence),
            'current_similar_index': self.current_similar_index,
            'total_similar_images': len(self.similar_images),
            'loop_count': self.loop_count,
            'total_play_time': self.total_play_time,
            'base_image_id': self.base_image_id
        }
    
    def skip_to_original_frame(self, frame_index: int) -> bool:
        """跳转到原图模式指定帧
        
        Args:
            frame_index: 帧索引
            
        Returns:
            bool: 是否成功跳转
        """
        if not self.original_mode_sequence or frame_index < 0 or frame_index >= len(self.original_mode_sequence):
            return False
        
        self.current_original_sequence_index = frame_index
        
        # 如果正在播放，重新安排播放
        if self.is_original_mode_playing and not self.is_paused:
            if self.play_timer_id:
                self.main_app.root.after_cancel(self.play_timer_id)
            
            self._play_next_original_frame()
        
        return True


# 工具函数
def format_duration(seconds: float) -> str:
    """格式化时间显示
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}分{secs:.1f}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}时{minutes}分{secs:.1f}秒"


def validate_play_speed(speed: float) -> float:
    """验证并修正播放速度
    
    Args:
        speed: 输入的播放速度
        
    Returns:
        float: 修正后的播放速度
    """
    return max(0.1, min(5.0, speed))


def generate_original_mode_script(base_image_id: int, timing_sequence: List[Tuple[int, int, float, int]], 
                                similar_images: List[Tuple[int, str, str]], mark_type: str = 'loop') -> str:
    """生成原图模式的脚本内容
    
    Args:
        base_image_id: 基础图片ID
        timing_sequence: 时间序列 [(from_image_id, to_image_id, duration, sequence_order), ...]
        similar_images: 相似图片列表 [(image_id, name, path), ...]
        mark_type: 标记类型
        
    Returns:
        str: 格式化的脚本内容
    """
    if not timing_sequence:
        return "没有时间序列数据"
    
    # 创建图片ID到名称的映射
    image_name_map = {img_id: img_name for img_id, img_name, _ in similar_images}
    
    script_lines = []
    script_lines.append("=" * 60)
    script_lines.append(f"原图模式播放脚本 - 基础图片ID: {base_image_id}")
    script_lines.append(f"标记类型: {mark_type}")
    script_lines.append(f"相似图片数量: {len(similar_images)}")
    script_lines.append(f"时间序列长度: {len(timing_sequence)}")
    script_lines.append("=" * 60)
    script_lines.append("")
    
    # 添加相似图片列表
    script_lines.append("相似图片列表:")
    for i, (img_id, img_name, img_path) in enumerate(similar_images):
        script_lines.append(f"  {i+1}. [{img_id}] {img_name}")
    script_lines.append("")
    
    # 添加时间序列
    script_lines.append("播放时间序列:")
    total_duration = 0
    
    for i, (from_id, to_id, duration, order) in enumerate(timing_sequence):
        from_name = image_name_map.get(from_id, f"未知图片({from_id})")
        to_name = image_name_map.get(to_id, f"未知图片({to_id})")
        
        script_lines.append(f"  {i+1:2d}. {from_name} → {to_name}")
        script_lines.append(f"      停留时间: {format_duration(duration)} ({duration:.2f}秒)")
        script_lines.append("")
        
        total_duration += duration
    
    script_lines.append(f"总播放时间: {format_duration(total_duration)} ({total_duration:.2f}秒)")
    script_lines.append("")
    
    # 添加播放说明
    script_lines.append("播放说明:")
    if mark_type == 'loop':
        script_lines.append("  - 循环模式：播放完最后一张图片后自动回到第一张")
    else:
        script_lines.append("  - 顺序模式：按顺序播放，到边界时切换到下一个不同系列的图片")
    script_lines.append("  - 可以通过播放速度控制播放节奏")
    script_lines.append("  - 支持暂停、恢复、跳转等操作")
    
    return "\n".join(script_lines)


def format_original_mode_script_for_edit(timing_sequence: List[Tuple[int, int, float, int]], 
                                       similar_images: List[Tuple[int, str, str]]) -> str:
    """格式化原图模式脚本用于编辑
    
    Args:
        timing_sequence: 时间序列
        similar_images: 相似图片列表
        
    Returns:
        str: 可编辑的脚本格式
    """
    if not timing_sequence:
        return ""
    
    # 创建图片ID到名称的映射
    image_name_map = {img_id: img_name for img_id, img_name, _ in similar_images}
    
    script_lines = []
    script_lines.append("# 原图模式时间序列编辑")
    script_lines.append("# 格式: 从图片ID -> 到图片ID : 停留时间(秒)")
    script_lines.append("# 示例: 123 -> 124 : 5.0")
    script_lines.append("")
    
    for from_id, to_id, duration, order in timing_sequence:
        from_name = image_name_map.get(from_id, f"未知({from_id})")
        to_name = image_name_map.get(to_id, f"未知({to_id})")
        
        script_lines.append(f"# {from_name} -> {to_name}")
        script_lines.append(f"{from_id} -> {to_id} : {duration:.2f}")
        script_lines.append("")
    
    return "\n".join(script_lines)


def parse_original_mode_script(script_content: str) -> List[Tuple[int, int, float]]:
    """解析原图模式脚本内容
    
    Args:
        script_content: 脚本内容
        
    Returns:
        List[Tuple[int, int, float]]: 解析后的时间序列 [(from_id, to_id, duration), ...]
    """
    timing_sequence = []
    
    for line in script_content.split('\n'):
        line = line.strip()
        
        # 跳过注释和空行
        if not line or line.startswith('#'):
            continue
        
        # 解析格式: from_id -> to_id : duration
        if ' -> ' in line and ' : ' in line:
            try:
                parts = line.split(' : ')
                if len(parts) == 2:
                    switch_part = parts[0].strip()
                    duration_part = parts[1].strip()
                    
                    if ' -> ' in switch_part:
                        from_to = switch_part.split(' -> ')
                        if len(from_to) == 2:
                            from_id = int(from_to[0].strip())
                            to_id = int(from_to[1].strip())
                            duration = float(duration_part)
                            
                            timing_sequence.append((from_id, to_id, duration))
            except (ValueError, IndexError) as e:
                print(f"解析脚本行失败: {line}, 错误: {e}")
                continue
    
    return timing_sequence


def validate_original_mode_timing_sequence(timing_sequence: List[Tuple[int, int, float]], 
                                         similar_images: List[Tuple[int, str, str]]) -> bool:
    """验证原图模式时间序列的有效性
    
    Args:
        timing_sequence: 时间序列
        similar_images: 相似图片列表
        
    Returns:
        bool: 是否有效
    """
    if not timing_sequence:
        return False
    
    # 获取所有相似图片的ID
    similar_image_ids = {img_id for img_id, _, _ in similar_images}
    
    for from_id, to_id, duration in timing_sequence:
        # 检查图片ID是否在相似图片列表中
        if from_id not in similar_image_ids:
            print(f"警告: 源图片ID {from_id} 不在相似图片列表中")
            return False
        
        if to_id not in similar_image_ids:
            print(f"警告: 目标图片ID {to_id} 不在相似图片列表中")
            return False
        
        # 检查时间是否合理
        if duration < 0:
            print(f"警告: 时间间隔不能为负数: {duration}")
            return False
        
        if duration > 3600:  # 超过1小时
            print(f"警告: 时间间隔过长: {duration}秒")
            return False
    
    return True
